/*
 * GP Storage OP-TEE Adapter Header for Trusty TEE
 * 
 * This header defines the complete GP storage architecture based on OP-TEE's
 * dual-layer object model (tee_obj + tee_pobj) with Trusty storage backend.
 */

#ifndef GP_STORAGE_OPTEE_ADAPTER_H
#define GP_STORAGE_OPTEE_ADAPTER_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <trusty_std.h>
#include <interface/storage/storage.h>
#include <lib/storage/storage.h>

/* ============================================================================
 * GP TEE标准定义
 * ============================================================================ */

/* GP TEE存储类型 */
#define TEE_STORAGE_PRIVATE     0x00000001
#define TEE_STORAGE_PERSO       0x00000002  
#define TEE_STORAGE_PROTECTED   0x00000004

/* GP TEE句柄标志 */
#define TEE_HANDLE_FLAG_PERSISTENT      0x00010000
#define TEE_HANDLE_FLAG_INITIALIZED     0x00020000

/* GP TEE对象使用权限 */
#define TEE_USAGE_EXTRACTABLE           0x00000001
#define TEE_USAGE_ENCRYPT               0x00000002
#define TEE_USAGE_DECRYPT               0x00000004
#define TEE_USAGE_MAC                   0x00000008
#define TEE_USAGE_SIGN                  0x00000010
#define TEE_USAGE_VERIFY                0x00000020
#define TEE_USAGE_DERIVE                0x00000040

/* GP TEE句柄定义 */
typedef uint32_t TEE_ObjectHandle;
typedef uint32_t TEE_ObjectEnumHandle;
#define TEE_HANDLE_NULL                 0

/* GP TEE错误码 */
typedef uint32_t TEE_Result;
#define TEE_SUCCESS                     0x00000000
#define TEE_ERROR_GENERIC               0xFFFF0000
#define TEE_ERROR_ACCESS_DENIED         0xFFFF0001
#define TEE_ERROR_ACCESS_CONFLICT       0xFFFF0003
#define TEE_ERROR_BAD_PARAMETERS        0xFFFF0006
#define TEE_ERROR_BAD_STATE             0xFFFF0007
#define TEE_ERROR_ITEM_NOT_FOUND        0xFFFF0008
#define TEE_ERROR_NOT_SUPPORTED         0xFFFF000A
#define TEE_ERROR_OUT_OF_MEMORY         0xFFFF000C
#define TEE_ERROR_BUSY                  0xFFFF000D
#define TEE_ERROR_STORAGE_NOT_AVAILABLE 0xFFFF3025
#define TEE_ERROR_CORRUPT_OBJECT        0xFFFF5002

/* GP TEE对象信息结构 */
typedef struct {
    uint32_t objectType;
    uint32_t objectSize;
    uint32_t maxObjectSize;
    uint32_t objectUsage;
    uint32_t dataSize;
    uint32_t dataPosition;
    uint32_t handleFlags;
} TEE_ObjectInfo;

/* GP TEE属性结构 */
typedef struct {
    uint32_t attributeID;
    union {
        struct {
            void *buffer;
            uint32_t length;
        } ref;
        struct {
            uint32_t a;
            uint32_t b;
        } value;
    } content;
} TEE_Attribute;

/* UUID结构定义 */
struct uuid {
    uint32_t time_low;
    uint16_t time_mid;
    uint16_t time_hi_and_version;
    uint8_t clock_seq_and_node[8];
};

/* ============================================================================
 * OP-TEE对象模型适配结构
 * ============================================================================ */

/* 前向声明 */
struct gp_tee_obj;
struct gp_pobj;
struct gp_storage_backend;

/* 持久对象使用类型 - 对应OP-TEE的tee_pobj_usage */
enum gp_pobj_usage {
    GP_POBJ_USAGE_OPEN,
    GP_POBJ_USAGE_RENAME,
    GP_POBJ_USAGE_CREATE,
    GP_POBJ_USAGE_ENUM,
};

/* GP TEE对象结构 - 完全基于OP-TEE tee_obj设计 */
struct gp_tee_obj {
    /* 链表管理 - 对应OP-TEE的TAILQ_ENTRY */
    struct list_node link;         /* TA私有对象链表节点 */
    
    /* GP标准对象信息 - 完全兼容OP-TEE */
    TEE_ObjectInfo info;           /* GP标准对象信息 */
    
    /* 并发控制 - 完全保持OP-TEE设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */
    
    /* 属性管理 - 完全保持OP-TEE设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */
    
    /* 数据流管理 - 完全保持OP-TEE设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */
    
    /* 存储抽象 - 适配Trusty机制 */
    struct gp_pobj *pobj;          /* 指向持久化对象的指针 */
    file_handle_t fh;              /* Trusty文件句柄 */
    
    /* Trusty特有扩展 */
    uint32_t handle_id;            /* 句柄唯一ID */
    mutex_t obj_lock;              /* 对象锁 */
};

/* GP持久对象结构 - 完全基于OP-TEE tee_pobj设计 */
struct gp_pobj {
    /* 链表管理 - 对应OP-TEE的TAILQ_ENTRY */
    struct list_node link;         /* 全局持久对象链表节点 */
    
    /* 引用计数 - 完全保持OP-TEE设计 */
    uint32_t refcnt;               /* 引用计数 */
    
    /* TA标识 - 完全保持OP-TEE设计 */
    struct uuid uuid;              /* TA UUID */
    void *obj_id;                  /* 对象ID */
    uint32_t obj_id_len;           /* 对象ID长度 */
    
    /* 访问控制 - 完全保持OP-TEE设计 */
    uint32_t flags;                /* 访问标志 */
    uint32_t obj_info_usage;       /* 使用权限 */
    
    /* 状态管理 - 完全保持OP-TEE设计 */
    bool temporary;                /* 创建过程中可修改 */
    bool creating;                 /* 正在创建状态 */
    
    /* 存储后端 - 适配Trusty机制 */
    struct gp_storage_backend *backend;  /* 存储后端接口 */
    char storage_path[512];              /* 存储路径 */
    
    /* 并发控制 */
    mutex_t pobj_lock;             /* 持久对象锁 */
};

/* GP存储后端接口 - 完全适配Trusty存储服务 */
struct gp_storage_backend {
    /* 会话管理 */
    storage_session_t session;        /* Trusty存储会话 */
    const char *port_name;            /* 存储服务端口名 */
    bool session_active;              /* 会话状态标志 */
    uint32_t storage_type;            /* 存储类型 */
    
    /* 基础文件操作 - 对应OP-TEE的tee_file_operations */
    TEE_Result (*open)(struct gp_pobj *pobj, size_t *size, file_handle_t *fh);
    TEE_Result (*create)(struct gp_pobj *pobj, bool overwrite,
                        const void *head, size_t head_size,
                        const void *attr, size_t attr_size,
                        file_handle_t *fh);
    TEE_Result (*close)(file_handle_t *fh);
    TEE_Result (*read)(file_handle_t fh, size_t pos, void *buf, size_t *len);
    TEE_Result (*write)(file_handle_t fh, size_t pos, const void *buf, size_t len);
    TEE_Result (*truncate)(file_handle_t fh, size_t len);
    TEE_Result (*remove)(struct gp_pobj *pobj);
    TEE_Result (*rename)(struct gp_pobj *old_pobj, struct gp_pobj *new_pobj, bool overwrite);
    
    /* 枚举操作 - 对应OP-TEE的目录操作 */
    TEE_Result (*opendir)(uint32_t storage_id, void **dir_handle);
    TEE_Result (*readdir)(void *dir_handle, struct gp_pobj **pobj);
    TEE_Result (*closedir)(void *dir_handle);
    
    /* 文件信息查询 */
    TEE_Result (*get_file_size)(file_handle_t fh, size_t *size);
    TEE_Result (*file_exists)(const char *path, bool *exists);
    
    /* 并发控制 */
    mutex_t backend_lock;             /* 后端锁 */
};

/* 目录枚举状态 - 适配Trusty存储目录操作 */
struct gp_dir_enum_state {
    storage_session_t session;           /* Trusty存储会话 */
    struct storage_open_dir_state state; /* Trusty目录枚举状态 */
    char prefix[512];                    /* 枚举路径前缀 */
    uint32_t storage_type;               /* 存储类型 */
    struct uuid ta_uuid;                 /* TA UUID */
    bool active;                         /* 枚举活跃状态 */
};

/* 存储管理器 */
struct gp_storage_manager {
    struct gp_storage_backend backends[3];  /* 三种存储类型的后端 */
    bool initialized;
    mutex_t manager_lock;
};

/* ============================================================================
 * OP-TEE对象管理方法声明 - 完全对应OP-TEE接口
 * ============================================================================ */

/* 对象管理 - 对应OP-TEE的tee_obj_* */
struct gp_tee_obj *gp_obj_alloc(void);
void gp_obj_free(struct gp_tee_obj *obj);
void gp_obj_add(struct gp_tee_obj *obj);
TEE_Result gp_obj_get(TEE_ObjectHandle object, struct gp_tee_obj **obj);
void gp_obj_close(struct gp_tee_obj *obj);
void gp_obj_close_all(void);
TEE_Result gp_obj_verify(struct gp_tee_obj *obj);

/* 持久对象管理 - 对应OP-TEE的tee_pobj_* */
TEE_Result gp_pobj_get(struct uuid *uuid, void *obj_id, uint32_t obj_id_len,
                       uint32_t flags, enum gp_pobj_usage usage,
                       struct gp_storage_backend *backend, struct gp_pobj **obj);
void gp_pobj_create_final(struct gp_pobj *obj);
TEE_Result gp_pobj_release(struct gp_pobj *obj);
TEE_Result gp_pobj_rename(struct gp_pobj *obj, void *obj_id, uint32_t obj_id_len);
void gp_pobj_lock_usage(struct gp_pobj *obj);
void gp_pobj_unlock_usage(struct gp_pobj *obj);

/* 并发控制 - 对应OP-TEE的并发机制 */
TEE_Result gp_obj_set_busy(struct gp_tee_obj *obj);
void gp_obj_clear_busy(struct gp_tee_obj *obj);

/* ============================================================================
 * 存储后端接口声明 - Trusty存储服务适配
 * ============================================================================ */

/* 存储后端管理 */
TEE_Result gp_storage_backend_init(struct gp_storage_backend *backend, uint32_t storage_type);
void gp_storage_backend_destroy(struct gp_storage_backend *backend);
struct gp_storage_backend *gp_get_storage_backend(uint32_t storage_type);

/* 错误码转换 */
TEE_Result gp_convert_storage_error(int storage_err);

/* 路径管理 */
int gp_build_ta_base_path(const struct uuid *ta_uuid, uint32_t storage_type,
                         char *path, size_t path_size);
int gp_build_object_path(const struct uuid *ta_uuid, const void *obj_id, 
                        uint32_t obj_id_len, uint32_t storage_type,
                        char *path, size_t path_size);

/* 辅助函数 */
void gp_uuid_to_string(const struct uuid *uuid, char *str, size_t str_size);
void gp_obj_id_to_hex(const void *obj_id, uint32_t obj_id_len, char *hex, size_t hex_size);
TEE_Result gp_parse_filename_to_pobj(const char *filename, const struct uuid *ta_uuid,
                                     uint32_t storage_type, struct gp_pobj **pobj);

/* 对象序列化 */
TEE_Result gp_serialize_object_attributes(struct gp_tee_obj *obj, void **data, size_t *size);
TEE_Result gp_create_object_header(struct gp_tee_obj *obj, void **header, size_t *size);
TEE_Result gp_load_object_header(struct gp_tee_obj *obj);
TEE_Result gp_update_object_header(struct gp_tee_obj *obj);
size_t gp_get_data_offset(struct gp_tee_obj *obj);

/* ============================================================================
 * GP存储API声明 - 26个标准API
 * ============================================================================ */

/* Generic Object Functions (5个) */
void TEE_GetObjectInfo1(TEE_ObjectHandle object, TEE_ObjectInfo *objectInfo);
TEE_Result TEE_RestrictObjectUsage1(TEE_ObjectHandle object, uint32_t objectUsage);
TEE_Result TEE_GetObjectBufferAttribute(TEE_ObjectHandle object, uint32_t attributeID,
                                       void *buffer, uint32_t *size);
TEE_Result TEE_GetObjectValueAttribute(TEE_ObjectHandle object, uint32_t attributeID,
                                      uint32_t *a, uint32_t *b);
void TEE_CloseObject(TEE_ObjectHandle object);

/* Transient Object Functions (7个) */
TEE_Result TEE_AllocateTransientObject(uint32_t objectType, uint32_t maxObjectSize,
                                      TEE_ObjectHandle *object);
void TEE_FreeTransientObject(TEE_ObjectHandle object);
void TEE_ResetTransientObject(TEE_ObjectHandle object);
TEE_Result TEE_PopulateTransientObject(TEE_ObjectHandle object,
                                      const TEE_Attribute *attrs, uint32_t attrCount);
void TEE_InitRefAttribute(TEE_Attribute *attr, uint32_t attributeID,
                         const void *buffer, uint32_t length);
void TEE_InitValueAttribute(TEE_Attribute *attr, uint32_t attributeID,
                           uint32_t a, uint32_t b);
void TEE_CopyObjectAttributes1(TEE_ObjectHandle destObject, TEE_ObjectHandle srcObject);

/* Persistent Object Functions (5个) */
TEE_Result TEE_OpenPersistentObject(uint32_t storageID, const void *objectID,
                                   uint32_t objectIDLen, uint32_t flags,
                                   TEE_ObjectHandle *object);
TEE_Result TEE_CreatePersistentObject(uint32_t storageID, const void *objectID,
                                     uint32_t objectIDLen, uint32_t flags,
                                     TEE_ObjectHandle attributes,
                                     const void *initialData, uint32_t initialDataLen,
                                     TEE_ObjectHandle *object);
TEE_Result TEE_CloseAndDeletePersistentObject1(TEE_ObjectHandle object);
TEE_Result TEE_RenamePersistentObject(TEE_ObjectHandle object,
                                     const void *newObjectID, uint32_t newObjectIDLen);

/* Persistent Object Enumeration Functions (5个) */
TEE_Result TEE_AllocatePersistentObjectEnumerator(TEE_ObjectEnumHandle *objectEnumerator);
void TEE_FreePersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator);
void TEE_ResetPersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator);
TEE_Result TEE_StartPersistentObjectEnumerator(TEE_ObjectEnumHandle objectEnumerator,
                                              uint32_t storageID);
TEE_Result TEE_GetNextPersistentObject(TEE_ObjectEnumHandle objectEnumerator,
                                      TEE_ObjectInfo *objectInfo, void *objectID,
                                      uint32_t *objectIDLen);

/* Data Stream Access Functions (4个) */
TEE_Result TEE_ReadObjectData(TEE_ObjectHandle object, void *buffer,
                             uint32_t size, uint32_t *count);
TEE_Result TEE_WriteObjectData(TEE_ObjectHandle object, const void *buffer, uint32_t size);
TEE_Result TEE_TruncateObjectData(TEE_ObjectHandle object, uint32_t size);
TEE_Result TEE_SeekObjectData(TEE_ObjectHandle object, int32_t offset, uint32_t whence);

/* ============================================================================
 * 常量和宏定义
 * ============================================================================ */

/* 最大值定义 */
#define GP_MAX_PATH_LEN             512
#define GP_MAX_OBJECT_ID_LEN        256
#define GP_MAX_FILENAME_LEN         255
#define GP_MAX_ATTR_COUNT           64

/* 数据流定位方式 */
#define TEE_DATA_SEEK_SET           0
#define TEE_DATA_SEEK_CUR           1
#define TEE_DATA_SEEK_END           2

/* 调试宏 */
#ifdef GP_STORAGE_DEBUG
#define GP_STORAGE_LOG(fmt, ...) \
    printf("[GP_STORAGE] " fmt "\n", ##__VA_ARGS__)
#else
#define GP_STORAGE_LOG(fmt, ...) do {} while (0)
#endif

/* 错误检查宏 */
#define GP_CHECK_PARAM(cond) \
    do { if (!(cond)) return TEE_ERROR_BAD_PARAMETERS; } while (0)

#define GP_CHECK_BACKEND(backend) \
    GP_CHECK_PARAM((backend) && (backend)->session_active)

#endif /* GP_STORAGE_OPTEE_ADAPTER_H */
