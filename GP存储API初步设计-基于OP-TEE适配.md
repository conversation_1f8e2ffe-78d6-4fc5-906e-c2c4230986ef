# Trusty TEE GP存储API初步设计 - 基于OP-TEE适配

## 1. 核心数据结构设计

### 1.1 GP对象句柄结构（基于OP-TEE tee_obj适配）

```c
/* GP TEE对象结构 - 完全基于OP-TEE tee_obj设计 */
struct gp_tee_obj {
    /* 链表管理 - 对应OP-TEE的TAILQ_ENTRY */
    struct list_node link;         /* TA私有对象链表节点 */
    
    /* GP标准对象信息 - 完全兼容OP-TEE */
    TEE_ObjectInfo info;           /* GP标准对象信息 */
    
    /* 并发控制 - 完全保持OP-TEE设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */
    
    /* 属性管理 - 完全保持OP-TEE设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */
    
    /* 数据流管理 - 完全保持OP-TEE设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */
    
    /* 存储抽象 - 适配Trusty机制 */
    struct gp_pobj *pobj;          /* 指向持久化对象的指针 */
    file_handle_t fh;              /* Trusty文件句柄 */
    
    /* Trusty特有扩展 */
    uint32_t handle_id;            /* 句柄唯一ID */
    mutex_t obj_lock;              /* 对象锁 */
};

/* GP持久对象结构 - 完全基于OP-TEE tee_pobj设计 */
struct gp_pobj {
    /* 链表管理 - 对应OP-TEE的TAILQ_ENTRY */
    struct list_node link;         /* 全局持久对象链表节点 */
    
    /* 引用计数 - 完全保持OP-TEE设计 */
    uint32_t refcnt;               /* 引用计数 */
    
    /* TA标识 - 完全保持OP-TEE设计 */
    struct uuid uuid;              /* TA UUID */
    void *obj_id;                  /* 对象ID */
    uint32_t obj_id_len;           /* 对象ID长度 */
    
    /* 访问控制 - 完全保持OP-TEE设计 */
    uint32_t flags;                /* 访问标志 */
    uint32_t obj_info_usage;       /* 使用权限 */
    
    /* 状态管理 - 完全保持OP-TEE设计 */
    bool temporary;                /* 创建过程中可修改 */
    bool creating;                 /* 正在创建状态 */
    
    /* 存储后端 - 适配Trusty机制 */
    storage_session_t session;     /* Trusty存储会话 */
    char storage_path[256];        /* 存储路径 */
    
    /* 并发控制 */
    mutex_t pobj_lock;             /* 持久对象锁 */
};
```

### 1.2 存储后端接口（基于OP-TEE tee_file_operations适配）

```c
/* Trusty存储操作接口 - 完全基于OP-TEE tee_file_operations设计 */
struct gp_storage_ops {
    /* 基础文件操作 - 对应OP-TEE接口 */
    TEE_Result (*open)(struct gp_pobj *pobj, size_t *size, file_handle_t *fh);
    TEE_Result (*create)(struct gp_pobj *pobj, bool overwrite,
                        const void *head, size_t head_size,
                        const void *attr, size_t attr_size,
                        file_handle_t *fh);
    TEE_Result (*close)(file_handle_t *fh);
    TEE_Result (*read)(file_handle_t fh, size_t pos, void *buf, size_t *len);
    TEE_Result (*write)(file_handle_t fh, size_t pos, const void *buf, size_t len);
    TEE_Result (*truncate)(file_handle_t fh, size_t len);
    TEE_Result (*remove)(struct gp_pobj *pobj);
    TEE_Result (*rename)(struct gp_pobj *old_pobj, struct gp_pobj *new_pobj, bool overwrite);
    
    /* 枚举操作 - 对应OP-TEE接口 */
    TEE_Result (*opendir)(uint32_t storage_id, void **dir_handle);
    TEE_Result (*readdir)(void *dir_handle, struct gp_pobj **pobj);
    TEE_Result (*closedir)(void *dir_handle);
};
```

## 2. 核心管理函数设计

### 2.1 对象管理函数（基于OP-TEE设计模式）

```c
/* 对象分配 - 对应OP-TEE的tee_obj_alloc */
struct gp_tee_obj *gp_obj_alloc(void) {
    struct gp_tee_obj *obj = calloc(1, sizeof(*obj));
    if (!obj)
        return NULL;
    
    list_initialize(&obj->link);
    mutex_init(&obj->obj_lock);
    obj->handle_id = gp_generate_handle_id();
    return obj;
}

/* 对象释放 - 对应OP-TEE的tee_obj_free */
void gp_obj_free(struct gp_tee_obj *obj) {
    if (!obj)
        return;
    
    if (obj->attr)
        free(obj->attr);
    
    mutex_destroy(&obj->obj_lock);
    free(obj);
}

/* 对象添加到TA - 对应OP-TEE的tee_obj_add */
void gp_obj_add(struct gp_tee_obj *obj) {
    /* 获取当前TA上下文 */
    struct ta_context *ta_ctx = get_current_ta_context();
    
    mutex_acquire(&ta_ctx->obj_list_lock);
    list_add_tail(&ta_ctx->obj_list, &obj->link);
    mutex_release(&ta_ctx->obj_list_lock);
}

/* 对象获取 - 对应OP-TEE的tee_obj_get */
TEE_Result gp_obj_get(TEE_ObjectHandle object, struct gp_tee_obj **obj) {
    struct ta_context *ta_ctx = get_current_ta_context();
    struct gp_tee_obj *o;
    
    mutex_acquire(&ta_ctx->obj_list_lock);
    list_for_every_entry(&ta_ctx->obj_list, o, struct gp_tee_obj, link) {
        if (o->handle_id == (uint32_t)object) {
            *obj = o;
            mutex_release(&ta_ctx->obj_list_lock);
            return TEE_SUCCESS;
        }
    }
    mutex_release(&ta_ctx->obj_list_lock);
    return TEE_ERROR_BAD_PARAMETERS;
}

/* 对象关闭 - 对应OP-TEE的tee_obj_close */
void gp_obj_close(struct gp_tee_obj *obj) {
    if (!obj)
        return;
    
    /* 从TA对象列表中移除 */
    struct ta_context *ta_ctx = get_current_ta_context();
    mutex_acquire(&ta_ctx->obj_list_lock);
    list_delete(&obj->link);
    mutex_release(&ta_ctx->obj_list_lock);
    
    /* 关闭文件句柄 */
    if (obj->fh != INVALID_FILE_HANDLE) {
        storage_close_file(obj->fh);
        obj->fh = INVALID_FILE_HANDLE;
    }
    
    /* 释放持久对象引用 */
    if (obj->pobj) {
        gp_pobj_put(obj->pobj);
        obj->pobj = NULL;
    }
    
    /* 释放对象 */
    gp_obj_free(obj);
}
```

### 2.2 持久对象管理函数（基于OP-TEE设计模式）

```c
/* 持久对象获取 - 对应OP-TEE的tee_pobj_get */
TEE_Result gp_pobj_get(struct uuid *uuid, void *obj_id, uint32_t obj_id_len,
                       uint32_t flags, struct gp_pobj **pobj) {
    struct gp_pobj *po;
    
    /* 在全局持久对象列表中查找 */
    mutex_acquire(&global_pobj_list_lock);
    list_for_every_entry(&global_pobj_list, po, struct gp_pobj, link) {
        if (uuid_equal(&po->uuid, uuid) &&
            po->obj_id_len == obj_id_len &&
            memcmp(po->obj_id, obj_id, obj_id_len) == 0) {
            
            /* 增加引用计数 */
            po->refcnt++;
            *pobj = po;
            mutex_release(&global_pobj_list_lock);
            return TEE_SUCCESS;
        }
    }
    
    /* 未找到，创建新的持久对象 */
    po = calloc(1, sizeof(*po));
    if (!po) {
        mutex_release(&global_pobj_list_lock);
        return TEE_ERROR_OUT_OF_MEMORY;
    }
    
    /* 初始化持久对象 */
    list_initialize(&po->link);
    po->refcnt = 1;
    po->uuid = *uuid;
    po->obj_id = malloc(obj_id_len);
    if (!po->obj_id) {
        free(po);
        mutex_release(&global_pobj_list_lock);
        return TEE_ERROR_OUT_OF_MEMORY;
    }
    memcpy(po->obj_id, obj_id, obj_id_len);
    po->obj_id_len = obj_id_len;
    po->flags = flags;
    mutex_init(&po->pobj_lock);
    
    /* 构建存储路径 */
    gp_build_storage_path(uuid, obj_id, obj_id_len, po->storage_path);
    
    /* 添加到全局列表 */
    list_add_tail(&global_pobj_list, &po->link);
    *pobj = po;
    mutex_release(&global_pobj_list_lock);
    
    return TEE_SUCCESS;
}

/* 持久对象释放 - 对应OP-TEE的tee_pobj_release */
TEE_Result gp_pobj_put(struct gp_pobj *pobj) {
    if (!pobj)
        return TEE_SUCCESS;
    
    mutex_acquire(&global_pobj_list_lock);
    if (--pobj->refcnt == 0) {
        /* 引用计数为0，从列表中移除并释放 */
        list_delete(&pobj->link);
        mutex_release(&global_pobj_list_lock);
        
        if (pobj->obj_id)
            free(pobj->obj_id);
        mutex_destroy(&pobj->pobj_lock);
        free(pobj);
    } else {
        mutex_release(&global_pobj_list_lock);
    }
    
    return TEE_SUCCESS;
}
```

## 3. 并发控制机制（基于OP-TEE设计）

### 3.1 对象忙状态管理

```c
/* 设置对象忙状态 - 对应OP-TEE逻辑 */
TEE_Result gp_obj_set_busy(struct gp_tee_obj *obj) {
    mutex_acquire(&obj->obj_lock);
    
    if (obj->busy) {
        mutex_release(&obj->obj_lock);
        return TEE_ERROR_BUSY;
    }
    
    obj->busy = true;
    mutex_release(&obj->obj_lock);
    return TEE_SUCCESS;
}

/* 清除对象忙状态 - 对应OP-TEE逻辑 */
void gp_obj_clear_busy(struct gp_tee_obj *obj) {
    mutex_acquire(&obj->obj_lock);
    obj->busy = false;
    mutex_release(&obj->obj_lock);
}

/* 持久对象使用权限锁定 - 对应OP-TEE的tee_pobj_lock_usage */
void gp_pobj_lock_usage(struct gp_pobj *pobj) {
    mutex_acquire(&pobj->pobj_lock);
}

/* 持久对象使用权限解锁 - 对应OP-TEE的tee_pobj_unlock_usage */
void gp_pobj_unlock_usage(struct gp_pobj *pobj) {
    mutex_release(&pobj->pobj_lock);
}
```

## 4. 26个GP存储API初步实现框架

### 4.1 Generic Object Functions（通用对象函数 - 5个）

```c
/* A.1.1 TEE_GetObjectInfo1 - 获取对象信息 */
void TEE_GetObjectInfo1(TEE_ObjectHandle object, TEE_ObjectInfo *objectInfo) {
    struct gp_tee_obj *obj;

    if (!objectInfo)
        return;

    if (gp_obj_get(object, &obj) != TEE_SUCCESS)
        return;

    *objectInfo = obj->info;
}

/* A.1.2 TEE_RestrictObjectUsage1 - 限制对象使用权限 */
TEE_Result TEE_RestrictObjectUsage1(TEE_ObjectHandle object, uint32_t objectUsage) {
    struct gp_tee_obj *obj;
    TEE_Result res;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    res = gp_obj_set_busy(obj);
    if (res != TEE_SUCCESS)
        return res;

    /* 只能收紧权限，不能扩展 */
    if ((objectUsage & ~obj->info.objectUsage) != 0) {
        gp_obj_clear_busy(obj);
        return TEE_ERROR_BAD_PARAMETERS;
    }

    obj->info.objectUsage &= objectUsage;
    gp_obj_clear_busy(obj);
    return TEE_SUCCESS;
}

/* A.1.3 TEE_GetObjectBufferAttribute - 获取缓冲区属性 */
TEE_Result TEE_GetObjectBufferAttribute(TEE_ObjectHandle object, uint32_t attributeID,
                                       void *buffer, uint32_t *size) {
    struct gp_tee_obj *obj;
    TEE_Result res;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    if (obj->busy)
        return TEE_ERROR_BUSY;

    return gp_find_buffer_attribute(obj, attributeID, buffer, size);
}

/* A.1.4 TEE_GetObjectValueAttribute - 获取值属性 */
TEE_Result TEE_GetObjectValueAttribute(TEE_ObjectHandle object, uint32_t attributeID,
                                      uint32_t *a, uint32_t *b) {
    struct gp_tee_obj *obj;
    TEE_Result res;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    if (obj->busy)
        return TEE_ERROR_BUSY;

    return gp_find_value_attribute(obj, attributeID, a, b);
}

/* A.1.5 TEE_CloseObject - 关闭对象 */
void TEE_CloseObject(TEE_ObjectHandle object) {
    struct gp_tee_obj *obj;

    if (gp_obj_get(object, &obj) != TEE_SUCCESS)
        return;

    gp_obj_close(obj);
}
```

### 4.2 Transient Object Functions（瞬态对象函数 - 7个）

```c
/* A.2.1 TEE_AllocateTransientObject - 分配瞬态对象 */
TEE_Result TEE_AllocateTransientObject(uint32_t objectType, uint32_t maxObjectSize,
                                      TEE_ObjectHandle *object) {
    struct gp_tee_obj *obj;

    if (!object)
        return TEE_ERROR_BAD_PARAMETERS;

    if (!gp_is_valid_object_type(objectType))
        return TEE_ERROR_NOT_SUPPORTED;

    obj = gp_obj_alloc();
    if (!obj)
        return TEE_ERROR_OUT_OF_MEMORY;

    /* 初始化瞬态对象信息 */
    obj->info.objectType = objectType;
    obj->info.maxObjectSize = maxObjectSize;
    obj->info.objectSize = 0;
    obj->info.objectUsage = 0xFFFFFFFF;  /* 所有权限 */
    obj->info.dataSize = 0;
    obj->info.dataPosition = 0;
    obj->info.handleFlags = 0;
    obj->pobj = NULL;  /* 瞬态对象无持久对象 */

    gp_obj_add(obj);
    *object = (TEE_ObjectHandle)obj->handle_id;
    return TEE_SUCCESS;
}

/* A.2.2 TEE_FreeTransientObject - 释放瞬态对象 */
void TEE_FreeTransientObject(TEE_ObjectHandle object) {
    struct gp_tee_obj *obj;

    if (gp_obj_get(object, &obj) != TEE_SUCCESS)
        return;

    /* 只能释放瞬态对象 */
    if (obj->pobj != NULL)
        return;

    gp_obj_close(obj);
}

/* A.2.3 TEE_ResetTransientObject - 重置瞬态对象 */
void TEE_ResetTransientObject(TEE_ObjectHandle object) {
    struct gp_tee_obj *obj;

    if (gp_obj_get(object, &obj) != TEE_SUCCESS)
        return;

    /* 只能重置瞬态对象 */
    if (obj->pobj != NULL)
        return;

    /* 清除属性和数据 */
    if (obj->attr) {
        free(obj->attr);
        obj->attr = NULL;
    }
    obj->have_attrs = 0;
    obj->info.objectSize = 0;
    obj->info.dataSize = 0;
    obj->info.dataPosition = 0;
}

/* A.2.4 TEE_PopulateTransientObject - 填充瞬态对象 */
TEE_Result TEE_PopulateTransientObject(TEE_ObjectHandle object,
                                      const TEE_Attribute *attrs, uint32_t attrCount) {
    struct gp_tee_obj *obj;
    TEE_Result res;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    /* 只能填充瞬态对象 */
    if (obj->pobj != NULL)
        return TEE_ERROR_BAD_PARAMETERS;

    res = gp_obj_set_busy(obj);
    if (res != TEE_SUCCESS)
        return res;

    /* 验证属性有效性 */
    res = gp_validate_attributes(obj->info.objectType, attrs, attrCount);
    if (res != TEE_SUCCESS) {
        gp_obj_clear_busy(obj);
        return res;
    }

    /* 复制属性到对象 */
    res = gp_copy_attributes(obj, attrs, attrCount);
    gp_obj_clear_busy(obj);
    return res;
}
```

### 4.3 Persistent Object Functions（持久对象函数 - 5个）

```c
/* A.3.1 TEE_OpenPersistentObject - 打开持久对象 */
TEE_Result TEE_OpenPersistentObject(uint32_t storageID, const void *objectID,
                                   uint32_t objectIDLen, uint32_t flags,
                                   TEE_ObjectHandle *object) {
    struct gp_tee_obj *obj;
    struct gp_pobj *pobj;
    struct uuid ta_uuid;
    TEE_Result res;
    int storage_err;

    if (!object || !objectID)
        return TEE_ERROR_BAD_PARAMETERS;

    if (!gp_is_valid_storage_id(storageID))
        return TEE_ERROR_BAD_PARAMETERS;

    /* 获取当前TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 获取或创建持久对象 */
    res = gp_pobj_get(&ta_uuid, (void *)objectID, objectIDLen, flags, &pobj);
    if (res != TEE_SUCCESS)
        return res;

    /* 分配对象句柄 */
    obj = gp_obj_alloc();
    if (!obj) {
        gp_pobj_put(pobj);
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 打开存储会话 */
    storage_err = storage_open_session(&pobj->session, STORAGE_CLIENT_TD_PORT);
    if (storage_err != NO_ERROR) {
        gp_obj_free(obj);
        gp_pobj_put(pobj);
        return gp_convert_storage_error(storage_err);
    }

    /* 打开文件 */
    storage_err = storage_open_file(pobj->session, &obj->fh, pobj->storage_path,
                                   flags, 0);
    if (storage_err != NO_ERROR) {
        storage_close_session(pobj->session);
        gp_obj_free(obj);
        gp_pobj_put(pobj);
        return gp_convert_storage_error(storage_err);
    }

    /* 读取对象信息 */
    res = gp_load_object_info(obj);
    if (res != TEE_SUCCESS) {
        storage_close_file(obj->fh);
        storage_close_session(pobj->session);
        gp_obj_free(obj);
        gp_pobj_put(pobj);
        return res;
    }

    obj->pobj = pobj;
    gp_obj_add(obj);
    *object = (TEE_ObjectHandle)obj->handle_id;
    return TEE_SUCCESS;
}

/* A.3.2 TEE_CreatePersistentObject - 创建持久对象 */
TEE_Result TEE_CreatePersistentObject(uint32_t storageID, const void *objectID,
                                     uint32_t objectIDLen, uint32_t flags,
                                     TEE_ObjectHandle attributes,
                                     const void *initialData, uint32_t initialDataLen,
                                     TEE_ObjectHandle *object) {
    struct gp_tee_obj *obj, *attr_obj = NULL;
    struct gp_pobj *pobj;
    struct uuid ta_uuid;
    TEE_Result res;
    int storage_err;

    if (!object || !objectID)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 获取属性对象（如果提供） */
    if (attributes != TEE_HANDLE_NULL) {
        res = gp_obj_get(attributes, &attr_obj);
        if (res != TEE_SUCCESS)
            return res;
    }

    /* 获取当前TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 创建持久对象 */
    res = gp_pobj_get(&ta_uuid, (void *)objectID, objectIDLen, flags, &pobj);
    if (res != TEE_SUCCESS)
        return res;

    pobj->creating = true;

    /* 分配对象句柄 */
    obj = gp_obj_alloc();
    if (!obj) {
        gp_pobj_put(pobj);
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 打开存储会话 */
    storage_err = storage_open_session(&pobj->session, STORAGE_CLIENT_TD_PORT);
    if (storage_err != NO_ERROR) {
        gp_obj_free(obj);
        gp_pobj_put(pobj);
        return gp_convert_storage_error(storage_err);
    }

    /* 创建文件 */
    storage_err = storage_open_file(pobj->session, &obj->fh, pobj->storage_path,
                                   flags | STORAGE_FILE_OPEN_CREATE, 0);
    if (storage_err != NO_ERROR) {
        storage_close_session(pobj->session);
        gp_obj_free(obj);
        gp_pobj_put(pobj);
        return gp_convert_storage_error(storage_err);
    }

    /* 初始化对象信息 */
    if (attr_obj) {
        obj->info = attr_obj->info;
        res = gp_copy_attributes(obj, attr_obj->attr, attr_obj->have_attrs);
        if (res != TEE_SUCCESS) {
            storage_close_file(obj->fh);
            storage_close_session(pobj->session);
            gp_obj_free(obj);
            gp_pobj_put(pobj);
            return res;
        }
    }

    /* 写入初始数据 */
    if (initialData && initialDataLen > 0) {
        storage_err = storage_write(obj->fh, 0, initialData, initialDataLen, 0);
        if (storage_err < 0) {
            storage_close_file(obj->fh);
            storage_close_session(pobj->session);
            gp_obj_free(obj);
            gp_pobj_put(pobj);
            return gp_convert_storage_error(storage_err);
        }
        obj->info.dataSize = initialDataLen;
    }

    /* 保存对象信息 */
    res = gp_save_object_info(obj);
    if (res != TEE_SUCCESS) {
        storage_close_file(obj->fh);
        storage_close_session(pobj->session);
        gp_obj_free(obj);
        gp_pobj_put(pobj);
        return res;
    }

    pobj->creating = false;
    obj->pobj = pobj;
    gp_obj_add(obj);
    *object = (TEE_ObjectHandle)obj->handle_id;
    return TEE_SUCCESS;
}

/* A.3.3 TEE_CloseAndDeletePersistentObject1 - 关闭并删除持久对象 */
TEE_Result TEE_CloseAndDeletePersistentObject1(TEE_ObjectHandle object) {
    struct gp_tee_obj *obj;
    TEE_Result res;
    int storage_err;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    /* 只能删除持久对象 */
    if (!obj->pobj)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 检查访问权限 */
    if (!(obj->info.handleFlags & TEE_HANDLE_FLAG_PERSISTENT))
        return TEE_ERROR_ACCESS_DENIED;

    /* 删除文件 */
    storage_err = storage_delete_file(obj->pobj->session, obj->pobj->storage_path, 0);
    if (storage_err != NO_ERROR && storage_err != ERR_NOT_FOUND)
        return gp_convert_storage_error(storage_err);

    /* 关闭对象 */
    gp_obj_close(obj);
    return TEE_SUCCESS;
}

/* A.3.4 TEE_RenamePersistentObject - 重命名持久对象 */
TEE_Result TEE_RenamePersistentObject(TEE_ObjectHandle object,
                                     const void *newObjectID, uint32_t newObjectIDLen) {
    struct gp_tee_obj *obj;
    struct gp_pobj *new_pobj;
    struct uuid ta_uuid;
    char new_path[256];
    TEE_Result res;
    int storage_err;

    if (!newObjectID)
        return TEE_ERROR_BAD_PARAMETERS;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    /* 只能重命名持久对象 */
    if (!obj->pobj)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 检查访问权限 */
    if (!(obj->info.handleFlags & TEE_HANDLE_FLAG_PERSISTENT))
        return TEE_ERROR_ACCESS_DENIED;

    /* 获取当前TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 构建新路径 */
    res = gp_build_storage_path(&ta_uuid, newObjectID, newObjectIDLen, new_path);
    if (res != TEE_SUCCESS)
        return res;

    /* 检查新对象是否已存在 */
    res = gp_pobj_get(&ta_uuid, (void *)newObjectID, newObjectIDLen, 0, &new_pobj);
    if (res == TEE_SUCCESS) {
        gp_pobj_put(new_pobj);
        return TEE_ERROR_ACCESS_CONFLICT;
    }

    /* 重命名文件 */
    storage_err = storage_move_file(obj->pobj->session, obj->pobj->storage_path,
                                   new_path, STORAGE_FILE_MOVE_CREATE);
    if (storage_err != NO_ERROR)
        return gp_convert_storage_error(storage_err);

    /* 更新持久对象信息 */
    gp_pobj_lock_usage(obj->pobj);
    free(obj->pobj->obj_id);
    obj->pobj->obj_id = malloc(newObjectIDLen);
    if (!obj->pobj->obj_id) {
        gp_pobj_unlock_usage(obj->pobj);
        return TEE_ERROR_OUT_OF_MEMORY;
    }
    memcpy(obj->pobj->obj_id, newObjectID, newObjectIDLen);
    obj->pobj->obj_id_len = newObjectIDLen;
    strcpy(obj->pobj->storage_path, new_path);
    gp_pobj_unlock_usage(obj->pobj);

    return TEE_SUCCESS;
}
```

### 4.4 Data Stream Access Functions（数据流访问函数 - 4个）

```c
/* A.5.1 TEE_ReadObjectData - 读取对象数据 */
TEE_Result TEE_ReadObjectData(TEE_ObjectHandle object, void *buffer,
                             uint32_t size, uint32_t *count) {
    struct gp_tee_obj *obj;
    TEE_Result res;
    ssize_t bytes_read;

    if (!buffer || !count)
        return TEE_ERROR_BAD_PARAMETERS;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    if (obj->busy)
        return TEE_ERROR_BUSY;

    /* 检查读权限 */
    if (!(obj->info.objectUsage & TEE_USAGE_EXTRACTABLE))
        return TEE_ERROR_ACCESS_DENIED;

    *count = 0;

    /* 瞬态对象没有数据流 */
    if (!obj->pobj)
        return TEE_SUCCESS;

    /* 从文件读取数据 */
    bytes_read = storage_read(obj->fh, obj->ds_pos, buffer, size);
    if (bytes_read < 0)
        return gp_convert_storage_error(bytes_read);

    *count = (uint32_t)bytes_read;
    obj->ds_pos += bytes_read;
    obj->info.dataPosition = obj->ds_pos;

    return TEE_SUCCESS;
}

/* A.5.2 TEE_WriteObjectData - 写入对象数据 */
TEE_Result TEE_WriteObjectData(TEE_ObjectHandle object, const void *buffer, uint32_t size) {
    struct gp_tee_obj *obj;
    TEE_Result res;
    ssize_t bytes_written;

    if (!buffer && size > 0)
        return TEE_ERROR_BAD_PARAMETERS;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    if (obj->busy)
        return TEE_ERROR_BUSY;

    /* 检查写权限 */
    if (!(obj->info.handleFlags & TEE_HANDLE_FLAG_PERSISTENT))
        return TEE_ERROR_ACCESS_DENIED;

    /* 瞬态对象不支持数据流写入 */
    if (!obj->pobj)
        return TEE_ERROR_ACCESS_DENIED;

    /* 写入文件 */
    bytes_written = storage_write(obj->fh, obj->ds_pos, buffer, size, 0);
    if (bytes_written < 0)
        return gp_convert_storage_error(bytes_written);

    if ((size_t)bytes_written != size)
        return TEE_ERROR_STORAGE_NOT_AVAILABLE;

    obj->ds_pos += size;
    obj->info.dataPosition = obj->ds_pos;

    /* 更新数据大小 */
    if (obj->ds_pos > obj->info.dataSize) {
        obj->info.dataSize = obj->ds_pos;
        res = gp_save_object_info(obj);
        if (res != TEE_SUCCESS)
            return res;
    }

    return TEE_SUCCESS;
}

/* A.5.3 TEE_TruncateObjectData - 截断对象数据 */
TEE_Result TEE_TruncateObjectData(TEE_ObjectHandle object, uint32_t size) {
    struct gp_tee_obj *obj;
    TEE_Result res;
    int storage_err;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    if (obj->busy)
        return TEE_ERROR_BUSY;

    /* 检查写权限 */
    if (!(obj->info.handleFlags & TEE_HANDLE_FLAG_PERSISTENT))
        return TEE_ERROR_ACCESS_DENIED;

    /* 瞬态对象不支持截断 */
    if (!obj->pobj)
        return TEE_ERROR_ACCESS_DENIED;

    /* 截断文件 */
    storage_err = storage_set_file_size(obj->fh, size, 0);
    if (storage_err != NO_ERROR)
        return gp_convert_storage_error(storage_err);

    /* 更新对象信息 */
    obj->info.dataSize = size;
    if (obj->ds_pos > size) {
        obj->ds_pos = size;
        obj->info.dataPosition = size;
    }

    res = gp_save_object_info(obj);
    return res;
}

/* A.5.4 TEE_SeekObjectData - 定位对象数据 */
TEE_Result TEE_SeekObjectData(TEE_ObjectHandle object, int32_t offset, TEE_Whence whence) {
    struct gp_tee_obj *obj;
    TEE_Result res;
    size_t new_pos;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    if (obj->busy)
        return TEE_ERROR_BUSY;

    /* 瞬态对象不支持定位 */
    if (!obj->pobj)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 计算新位置 */
    switch (whence) {
        case TEE_DATA_SEEK_SET:
            if (offset < 0)
                return TEE_ERROR_BAD_PARAMETERS;
            new_pos = (size_t)offset;
            break;
        case TEE_DATA_SEEK_CUR:
            if (offset < 0 && (size_t)(-offset) > obj->ds_pos)
                return TEE_ERROR_BAD_PARAMETERS;
            new_pos = obj->ds_pos + offset;
            break;
        case TEE_DATA_SEEK_END:
            if (offset < 0 && (size_t)(-offset) > obj->info.dataSize)
                return TEE_ERROR_BAD_PARAMETERS;
            new_pos = obj->info.dataSize + offset;
            break;
        default:
            return TEE_ERROR_BAD_PARAMETERS;
    }

    obj->ds_pos = new_pos;
    obj->info.dataPosition = new_pos;
    return TEE_SUCCESS;
}
```

## 5. 辅助函数实现

### 5.1 存储路径构建和错误转换

```c
/* 构建存储路径 - 基于TA UUID和对象ID */
TEE_Result gp_build_storage_path(struct uuid *ta_uuid, const void *obj_id,
                                uint32_t obj_id_len, char *path) {
    char uuid_str[37];  /* UUID字符串格式 */
    char obj_id_hex[512];  /* 对象ID十六进制格式 */

    /* 转换UUID为字符串 */
    snprintf(uuid_str, sizeof(uuid_str),
             "%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x",
             ta_uuid->time_low, ta_uuid->time_mid, ta_uuid->time_hi_and_version,
             ta_uuid->clock_seq_and_node[0], ta_uuid->clock_seq_and_node[1],
             ta_uuid->clock_seq_and_node[2], ta_uuid->clock_seq_and_node[3],
             ta_uuid->clock_seq_and_node[4], ta_uuid->clock_seq_and_node[5],
             ta_uuid->clock_seq_and_node[6], ta_uuid->clock_seq_and_node[7]);

    /* 转换对象ID为十六进制字符串 */
    const uint8_t *id_bytes = (const uint8_t *)obj_id;
    for (uint32_t i = 0; i < obj_id_len && i < 255; i++) {
        snprintf(&obj_id_hex[i * 2], 3, "%02x", id_bytes[i]);
    }

    /* 构建完整路径：/ta_uuid/obj_id_hex */
    snprintf(path, 256, "%s/%s", uuid_str, obj_id_hex);

    return TEE_SUCCESS;
}

/* Trusty存储错误码转换为GP错误码 */
TEE_Result gp_convert_storage_error(int storage_err) {
    switch (storage_err) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_NOT_FOUND:
            return TEE_ERROR_ITEM_NOT_FOUND;
        case ERR_ACCESS_DENIED:
            return TEE_ERROR_ACCESS_DENIED;
        case ERR_ALREADY_EXISTS:
            return TEE_ERROR_ACCESS_CONFLICT;
        case ERR_NO_MEMORY:
            return TEE_ERROR_OUT_OF_MEMORY;
        case ERR_IO:
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        case ERR_INVALID_ARGS:
            return TEE_ERROR_BAD_PARAMETERS;
        default:
            return TEE_ERROR_GENERIC;
    }
}
```
```
