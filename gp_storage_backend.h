/*
 * GP Storage Backend Header for Trusty TEE
 * 
 * This header defines the GP storage backend interface that adapts
 * Trusty storage services for GP TEE Internal Core API compliance.
 */

#ifndef GP_STORAGE_BACKEND_H
#define GP_STORAGE_BACKEND_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <interface/storage/storage.h>
#include <lib/storage/storage.h>

/* GP TEE存储类型定义 */
#define TEE_STORAGE_PRIVATE     0x00000001
#define TEE_STORAGE_PERSO       0x00000002  
#define TEE_STORAGE_PROTECTED   0x00000004

/* GP TEE错误码定义 */
typedef uint32_t TEE_Result;

#define TEE_SUCCESS                     0x00000000
#define TEE_ERROR_GENERIC               0xFFFF0000
#define TEE_ERROR_ACCESS_DENIED         0xFFFF0001
#define TEE_ERROR_CANCEL                0xFFFF0002
#define TEE_ERROR_ACCESS_CONFLICT       0xFFFF0003
#define TEE_ERROR_EXCESS_DATA           0xFFFF0004
#define TEE_ERROR_BAD_FORMAT            0xFFFF0005
#define TEE_ERROR_BAD_PARAMETERS        0xFFFF0006
#define TEE_ERROR_BAD_STATE             0xFFFF0007
#define TEE_ERROR_ITEM_NOT_FOUND        0xFFFF0008
#define TEE_ERROR_NOT_IMPLEMENTED       0xFFFF0009
#define TEE_ERROR_NOT_SUPPORTED         0xFFFF000A
#define TEE_ERROR_NO_DATA               0xFFFF000B
#define TEE_ERROR_OUT_OF_MEMORY         0xFFFF000C
#define TEE_ERROR_BUSY                  0xFFFF000D
#define TEE_ERROR_COMMUNICATION         0xFFFF000E
#define TEE_ERROR_SECURITY              0xFFFF000F
#define TEE_ERROR_SHORT_BUFFER          0xFFFF0010
#define TEE_ERROR_EXTERNAL_CANCEL       0xFFFF0011
#define TEE_ERROR_OVERFLOW              0xFFFF300F
#define TEE_ERROR_TARGET_DEAD           0xFFFF3024
#define TEE_ERROR_STORAGE_NOT_AVAILABLE 0xFFFF3025
#define TEE_ERROR_STORAGE_NO_SPACE      0xFFFF3041
#define TEE_ERROR_MAC_INVALID           0xFFFF3071
#define TEE_ERROR_SIGNATURE_INVALID     0xFFFF3072
#define TEE_ERROR_TIME_NOT_SET          0xFFFF5000
#define TEE_ERROR_TIME_NEEDS_RESET      0xFFFF5001
#define TEE_ERROR_CORRUPT_OBJECT        0xFFFF5002

/* UUID结构定义 */
struct uuid {
    uint32_t time_low;
    uint16_t time_mid;
    uint16_t time_hi_and_version;
    uint8_t clock_seq_and_node[8];
};

/* 前向声明 */
struct gp_storage_backend;

/* 目录枚举状态结构 */
struct gp_dir_enum_state {
    storage_session_t session;           /* 存储会话 */
    struct storage_open_dir_state state; /* Trusty目录状态 */
    char prefix[256];                    /* 枚举前缀 */
    bool active;                         /* 枚举活跃状态 */
};

/* GP存储后端接口结构 */
struct gp_storage_backend {
    /* 会话管理 */
    storage_session_t session;        /* Trusty存储会话 */
    const char *port_name;            /* 存储服务端口名 */
    bool session_active;              /* 会话状态标志 */
    
    /* 基础文件操作函数指针 */
    int (*open)(struct gp_storage_backend *backend, const char *path, 
                uint32_t flags, file_handle_t *fh);
    int (*create)(struct gp_storage_backend *backend, const char *path, 
                  uint32_t flags, const void *data, size_t size, file_handle_t *fh);
    int (*close)(struct gp_storage_backend *backend, file_handle_t fh);
    int (*read)(struct gp_storage_backend *backend, file_handle_t fh, 
                storage_off_t offset, void *buf, size_t size, size_t *bytes_read);
    int (*write)(struct gp_storage_backend *backend, file_handle_t fh, 
                 storage_off_t offset, const void *buf, size_t size);
    int (*truncate)(struct gp_storage_backend *backend, file_handle_t fh, 
                    storage_off_t size);
    int (*remove)(struct gp_storage_backend *backend, const char *path);
    int (*rename)(struct gp_storage_backend *backend, const char *old_path, 
                  const char *new_path);
    
    /* 文件信息查询函数指针 */
    int (*get_size)(struct gp_storage_backend *backend, file_handle_t fh, 
                    storage_off_t *size);
    int (*exists)(struct gp_storage_backend *backend, const char *path, 
                  bool *exists);
    
    /* 目录枚举操作函数指针 */
    int (*list_begin)(struct gp_storage_backend *backend, const char *prefix, 
                      void **iter_handle);
    int (*list_next)(struct gp_storage_backend *backend, void *iter_handle, 
                     char *name, size_t name_size, bool *has_more);
    int (*list_end)(struct gp_storage_backend *backend, void *iter_handle);
    
    /* 事务支持函数指针 */
    int (*begin_transaction)(struct gp_storage_backend *backend);
    int (*commit_transaction)(struct gp_storage_backend *backend);
    int (*abort_transaction)(struct gp_storage_backend *backend);
};

/* GP存储会话上下文 */
struct gp_storage_context {
    struct gp_storage_backend *backend;  /* 存储后端接口 */
    struct uuid ta_uuid;                 /* 当前TA UUID */
    char base_path[64];                  /* TA基础路径 */
    uint32_t storage_type;               /* 存储类型 */
    mutex_t ctx_lock;                    /* 上下文锁 */
};

/* ============================================================================
 * 公共接口函数声明
 * ============================================================================ */

/**
 * 初始化存储后端
 * 
 * @param backend 存储后端结构指针
 * @param storage_type 存储类型 (TEE_STORAGE_PRIVATE/PERSO/PROTECTED)
 * @return NO_ERROR 成功，其他值表示错误
 */
int gp_storage_backend_init(struct gp_storage_backend *backend, uint32_t storage_type);

/**
 * 销毁存储后端
 * 
 * @param backend 存储后端结构指针
 */
void gp_storage_backend_destroy(struct gp_storage_backend *backend);

/**
 * 获取存储后端实例（单例模式）
 * 
 * @param storage_type 存储类型
 * @return 存储后端指针，失败返回NULL
 */
struct gp_storage_backend *gp_get_storage_backend(uint32_t storage_type);

/**
 * Trusty存储错误码到GP错误码映射
 * 
 * @param storage_err Trusty存储错误码
 * @return GP TEE错误码
 */
TEE_Result gp_convert_storage_error(int storage_err);

/**
 * 构建TA隔离的存储路径
 * 
 * @param ta_uuid TA UUID
 * @param obj_id 对象ID
 * @param obj_id_len 对象ID长度
 * @param storage_type 存储类型
 * @param path 输出路径缓冲区
 * @param path_size 路径缓冲区大小
 * @return NO_ERROR 成功，其他值表示错误
 */
int gp_build_ta_storage_path(const struct uuid *ta_uuid, 
                            const void *obj_id, uint32_t obj_id_len,
                            uint32_t storage_type, char *path, size_t path_size);

/* ============================================================================
 * 内联辅助函数
 * ============================================================================ */

/**
 * 检查存储类型是否有效
 */
static inline bool gp_is_valid_storage_type(uint32_t storage_type) {
    return (storage_type == TEE_STORAGE_PRIVATE ||
            storage_type == TEE_STORAGE_PERSO ||
            storage_type == TEE_STORAGE_PROTECTED);
}

/**
 * 检查UUID是否有效
 */
static inline bool gp_is_valid_uuid(const struct uuid *uuid) {
    return (uuid != NULL);
}

/**
 * UUID比较
 */
static inline bool gp_uuid_equal(const struct uuid *uuid1, const struct uuid *uuid2) {
    if (!uuid1 || !uuid2)
        return false;
    
    return (uuid1->time_low == uuid2->time_low &&
            uuid1->time_mid == uuid2->time_mid &&
            uuid1->time_hi_and_version == uuid2->time_hi_and_version &&
            memcmp(uuid1->clock_seq_and_node, uuid2->clock_seq_and_node, 8) == 0);
}

/* ============================================================================
 * 宏定义
 * ============================================================================ */

/* 最大路径长度 */
#define GP_MAX_PATH_LEN         512

/* 最大对象ID长度 */
#define GP_MAX_OBJECT_ID_LEN    256

/* 最大文件名长度 */
#define GP_MAX_FILENAME_LEN     255

/* 存储后端调用宏 */
#define GP_STORAGE_CALL(backend, func, ...) \
    ((backend) && (backend)->func ? (backend)->func(backend, ##__VA_ARGS__) : -EINVAL)

/* 错误检查宏 */
#define GP_CHECK_PARAM(cond) \
    do { if (!(cond)) return -EINVAL; } while (0)

#define GP_CHECK_BACKEND(backend) \
    GP_CHECK_PARAM((backend) && (backend)->session_active)

/* 调试宏 */
#ifdef GP_STORAGE_DEBUG
#define GP_STORAGE_LOG(fmt, ...) \
    printf("[GP_STORAGE] " fmt "\n", ##__VA_ARGS__)
#else
#define GP_STORAGE_LOG(fmt, ...) do {} while (0)
#endif

#endif /* GP_STORAGE_BACKEND_H */
